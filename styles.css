/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans Tamil', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 50%, #45b7d1 100%);
    color: white;
    padding: 1rem 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h1 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.2rem;
}

.logo span {
    font-size: 1rem;
    opacity: 0.9;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: white;
    text-decoration: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    background-color: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

.nav-link i {
    font-size: 1.2rem;
    margin-bottom: 0.3rem;
}

.nav-link span {
    font-size: 0.9rem;
}

.mobile-menu-toggle {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Main Content */
.main {
    margin-top: 80px;
}

.section {
    display: none;
    min-height: calc(100vh - 80px);
    padding: 2rem 0;
}

.section.active {
    display: block;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #333;
    font-weight: 600;
}

/* Search Section */
.search-section {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
    padding: 1rem 0;
    margin-top: 80px;
}

.search-bar {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 50px;
    padding: 0.5rem 1rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    max-width: 600px;
    margin: 0 auto;
}

.search-bar i {
    color: #ff6b6b;
    margin-right: 1rem;
    font-size: 1.2rem;
}

.search-bar input {
    flex: 1;
    border: none;
    outline: none;
    padding: 0.8rem;
    font-size: 1rem;
    font-family: inherit;
}

.search-btn {
    background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 50%, #45b7d1 100%);
    color: white;
    padding: 4rem 0;
}

.hero-content h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.hero-content p {
    font-size: 1.3rem;
    margin-bottom: 3rem;
    opacity: 0.9;
}

.hero-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    background: rgba(255,255,255,0.1);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    backdrop-filter: blur(10px);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-card i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #ffd700;
}

.feature-card h3 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
}

/* Category Grid */
.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.category-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    border-color: #667eea;
}

.category-card.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.category-card i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #667eea;
}

.category-card.active i {
    color: white;
}

.category-card h3 {
    font-size: 1.2rem;
    font-weight: 600;
}

/* FM Player */
.fm-player {
    max-width: 600px;
    margin: 0 auto;
}

.player-container {
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
}

.player-info h3 {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
    color: #333;
}

.player-info p {
    color: #666;
    margin-bottom: 2rem;
}

.player-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 2rem;
}

.play-btn {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.play-btn:hover {
    transform: scale(1.1);
}

.volume-control {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.volume-slider {
    width: 100px;
}

.now-playing {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 10px;
    font-weight: 500;
}

/* Content Areas */
.news-content,
.video-content,
.photo-gallery,
.helpline-content {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    min-height: 300px;
}

/* Magazine Grid */
.magazine-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.magazine-item {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.magazine-item:hover {
    transform: translateY(-5px);
}

.magazine-cover {
    height: 200px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.magazine-info {
    padding: 1.5rem;
}

.magazine-info h3 {
    margin-bottom: 0.5rem;
    color: #333;
}

.magazine-info p {
    color: #666;
    margin-bottom: 1rem;
}

.download-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.download-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
    }
    
    .nav {
        display: none;
    }
    
    .hero-content h1 {
        font-size: 2rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .category-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }
    
    .category-card {
        padding: 1.5rem;
    }
    
    .feature-card {
        padding: 1.5rem;
    }
    
    .player-container {
        padding: 2rem;
    }
    
    .player-controls {
        flex-direction: column;
        gap: 1rem;
    }
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.mb-2 {
    margin-bottom: 1rem;
}

.mb-3 {
    margin-bottom: 1.5rem;
}

.btn {
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 2rem;
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    position: relative;
    max-height: 80vh;
    overflow-y: auto;
}

.close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    font-size: 2rem;
    cursor: pointer;
    color: #666;
}

.close:hover {
    color: #333;
}

/* Comment System */
.comment-form {
    margin-bottom: 2rem;
}

.comment-form textarea {
    width: 100%;
    min-height: 100px;
    padding: 1rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-family: inherit;
    resize: vertical;
    margin-bottom: 1rem;
}

.comment-form textarea:focus {
    outline: none;
    border-color: #667eea;
}

.submit-comment {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
}

.comments-list {
    max-height: 300px;
    overflow-y: auto;
}

.comment-item {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.comment-author {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.comment-text {
    color: #666;
    line-height: 1.5;
}

.comment-time {
    font-size: 0.8rem;
    color: #999;
    margin-top: 0.5rem;
}

/* Interaction Buttons */
.interaction-bar {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 0;
    border-top: 1px solid #e9ecef;
    margin-top: 1rem;
}

.interaction-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.interaction-btn:hover {
    background: #f8f9fa;
    color: #333;
}

.interaction-btn.active {
    background: #667eea;
    color: white;
}

.emoji-picker {
    display: none;
    position: absolute;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    z-index: 1000;
}

.emoji-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 0.5rem;
}

.emoji-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.emoji-btn:hover {
    background: #f8f9fa;
}

/* Content Cards */
.content-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    transition: transform 0.3s ease;
}

.content-card:hover {
    transform: translateY(-2px);
}

.content-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.content-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.content-meta {
    color: #666;
    font-size: 0.9rem;
}

.content-body {
    padding: 1.5rem;
}

.content-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

/* Video Player */
.video-player {
    position: relative;
    width: 100%;
    height: 300px;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
}

.video-iframe {
    width: 100%;
    height: 100%;
    border: none;
}

/* Photo Gallery */
.photo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.photo-item {
    position: relative;
    aspect-ratio: 1;
    overflow: hidden;
    border-radius: 8px;
    cursor: pointer;
}

.photo-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.photo-item:hover img {
    transform: scale(1.1);
}

.photo-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.7));
    color: white;
    padding: 1rem;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.photo-item:hover .photo-overlay {
    transform: translateY(0);
}

/* Help Line */
.helpline-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.helpline-item:last-child {
    border-bottom: none;
}

.helpline-info h4 {
    color: #333;
    margin-bottom: 0.3rem;
}

.helpline-info p {
    color: #666;
    font-size: 0.9rem;
}

.call-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 0.8rem 1.2rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.call-btn:hover {
    background: #218838;
    transform: translateY(-2px);
}

/* Loading Animation */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* Mobile Responsive Updates */
@media (max-width: 480px) {
    .modal-content {
        margin: 10% auto;
        padding: 1.5rem;
        width: 95%;
    }

    .interaction-bar {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .photo-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .helpline-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .call-btn {
        align-self: stretch;
        justify-content: center;
    }
}

/* Enhanced Video Player Styles */
.video-player {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    background: #000;
    border-radius: 8px;
    overflow: hidden;
}

.video-iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
}

/* Enhanced Photo Gallery Styles */
.photo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.photo-item {
    position: relative;
    aspect-ratio: 4/3;
    overflow: hidden;
    border-radius: 12px;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.photo-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.photo-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.photo-item:hover img {
    transform: scale(1.05);
}

.photo-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: white;
    padding: 1.5rem;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.photo-item:hover .photo-overlay {
    transform: translateY(0);
}

.photo-overlay h4 {
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.photo-overlay p {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Enhanced Helpline Styles */
.helpline-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
    transition: background 0.3s ease;
}

.helpline-item:hover {
    background: #f8f9fa;
}

.helpline-item:last-child {
    border-bottom: none;
}

.helpline-info h4 {
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.helpline-info p {
    color: #666;
    font-size: 0.9rem;
}

.call-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 1rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.call-btn:hover {
    background: #218838;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

/* Enhanced Magazine Styles */
.magazine-item {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.magazine-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.magazine-cover {
    height: 200px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
    padding: 2rem;
}

.magazine-cover i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.magazine-cover h3 {
    font-size: 1.3rem;
    font-weight: 600;
}

.magazine-info {
    padding: 1.5rem;
}

.magazine-info h3 {
    margin-bottom: 0.5rem;
    color: #333;
    font-size: 1.2rem;
}

.magazine-info p {
    color: #666;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
}

/* Enhanced FM Player Styles */
.player-container {
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.player-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.player-info h3 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 600;
}

.player-info p {
    color: #666;
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.player-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 3rem;
    margin-bottom: 2rem;
}

.play-btn {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    font-size: 2.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.play-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

.volume-control {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.volume-control i {
    color: #667eea;
    font-size: 1.2rem;
}

.volume-slider {
    width: 120px;
    height: 6px;
    border-radius: 3px;
    background: #e9ecef;
    outline: none;
    -webkit-appearance: none;
}

.volume-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

.volume-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

.now-playing {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1.5rem;
    border-radius: 12px;
    font-weight: 500;
    color: #333;
    border-left: 4px solid #667eea;
}

/* Enhanced Content Cards */
.content-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    margin-bottom: 2rem;
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
}

.content-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.12);
    border-color: #667eea;
}

.content-header {
    padding: 1.5rem;
    border-bottom: 1px solid #f0f0f0;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.content-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.content-meta {
    color: #666;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.content-meta::before {
    content: '📅';
    font-size: 0.8rem;
}

.content-body {
    padding: 1.5rem;
}

.content-body p {
    line-height: 1.6;
    color: #555;
    margin-bottom: 1rem;
}

.content-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 1rem;
}

/* Enhanced Interaction Bar */
.interaction-bar {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem 0;
    border-top: 1px solid #f0f0f0;
    margin-top: 1rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 0 0 15px 15px;
    margin: 1rem -1.5rem -1.5rem -1.5rem;
    padding: 1rem 1.5rem;
}

.interaction-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: 2px solid #e9ecef;
    color: #666;
    cursor: pointer;
    padding: 0.8rem 1.2rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
}

.interaction-btn:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.interaction-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.interaction-btn i {
    font-size: 1rem;
}

/* Enhanced Category Cards */
.category-card {
    background: white;
    padding: 2.5rem 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.category-card:hover::before {
    transform: scaleX(1);
}

.category-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    border-color: #667eea;
}

.category-card.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
}

.category-card.active::before {
    transform: scaleX(1);
    background: rgba(255,255,255,0.3);
}

.category-card i {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    color: #667eea;
    transition: all 0.3s ease;
}

.category-card.active i {
    color: white;
    transform: scale(1.1);
}

.category-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

/* Enhanced Loading Animation */
.loading {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 300px;
    gap: 1rem;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading::after {
    content: 'ஏற்றுகிறது...';
    color: #666;
    font-size: 1.1rem;
    font-weight: 500;
}

/* Enhanced Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.6);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 3% auto;
    padding: 2rem;
    border-radius: 20px;
    width: 90%;
    max-width: 700px;
    position: relative;
    max-height: 85vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.close {
    position: absolute;
    right: 1.5rem;
    top: 1.5rem;
    font-size: 2rem;
    cursor: pointer;
    color: #666;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.close:hover {
    color: #333;
    background: #f8f9fa;
}

/* Enhanced Comment System */
.comment-form {
    margin-bottom: 2rem;
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 12px;
}

.comment-form textarea {
    width: 100%;
    min-height: 120px;
    padding: 1rem;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-family: inherit;
    resize: vertical;
    margin-bottom: 1rem;
    transition: border-color 0.3s ease;
}

.comment-form textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.submit-comment {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.submit-comment:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.comments-list {
    max-height: 400px;
    overflow-y: auto;
}

.comment-item {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 1rem;
    border-left: 4px solid #667eea;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.comment-author {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.comment-text {
    color: #555;
    line-height: 1.6;
    margin-bottom: 0.5rem;
}

.comment-time {
    font-size: 0.8rem;
    color: #999;
}

/* Responsive Enhancements */
@media (max-width: 992px) {
    .hero-content h1 {
        font-size: 2.5rem;
    }

    .section-title {
        font-size: 2.2rem;
    }

    .category-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }
}

@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2rem;
    }

    .hero-content p {
        font-size: 1.1rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .category-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }

    .category-card {
        padding: 1.5rem 1rem;
    }

    .category-card i {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    .category-card h3 {
        font-size: 1.1rem;
    }

    .player-container {
        padding: 2rem;
    }

    .player-controls {
        flex-direction: column;
        gap: 1.5rem;
    }

    .play-btn {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }

    .interaction-bar {
        flex-wrap: wrap;
        gap: 0.8rem;
    }

    .interaction-btn {
        padding: 0.6rem 1rem;
        font-size: 0.8rem;
    }

    .modal-content {
        margin: 5% auto;
        padding: 1.5rem;
        width: 95%;
    }
}

@media (max-width: 480px) {
    .hero-content h1 {
        font-size: 1.8rem;
    }

    .section-title {
        font-size: 1.6rem;
    }

    .category-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    }

    .photo-grid {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    }

    .helpline-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
        text-align: left;
    }

    .call-btn {
        align-self: stretch;
        justify-content: center;
    }

    .player-info h3 {
        font-size: 1.5rem;
    }

    .content-title {
        font-size: 1.2rem;
    }
}

/* Sidebar Layout */
.sidebar-layout {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.sidebar {
    width: 280px;
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    height: fit-content;
    position: sticky;
    top: 100px;
}

.sidebar h3 {
    color: #333;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    border-bottom: 3px solid #ff6b6b;
    padding-bottom: 0.5rem;
}

.sidebar-menu {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.sidebar-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.sidebar-item:hover {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    transform: translateX(5px);
    border-color: #ff6b6b;
}

.sidebar-item.active {
    background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
    color: white;
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.sidebar-item i {
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

.sidebar-item span {
    font-weight: 500;
}

.main-content {
    flex: 1;
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f0f0f0;
}

.view-options {
    display: flex;
    gap: 0.5rem;
}

.view-btn {
    padding: 0.8rem 1.2rem;
    border: 2px solid #e9ecef;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.view-btn.active,
.view-btn:hover {
    background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
    color: white;
    border-color: #ff6b6b;
}

.sort-options select {
    padding: 0.8rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background: white;
    font-family: inherit;
    cursor: pointer;
}

.welcome-message {
    text-align: center;
    padding: 4rem 2rem;
    color: #666;
}

.welcome-message i {
    font-size: 4rem;
    color: #ff6b6b;
    margin-bottom: 1rem;
}

.welcome-message h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #333;
}

/* FM Player Enhanced Styles */
.fm-layout {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.fm-player {
    flex: 2;
}

.player-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.player-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.station-info h3 {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
}

.station-tagline {
    opacity: 0.8;
    font-size: 1rem;
}

.live-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255,255,255,0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

.live-dot {
    width: 8px;
    height: 8px;
    background: #ff4757;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.current-track {
    background: rgba(255,255,255,0.1);
    padding: 1.5rem;
    border-radius: 15px;
    margin-bottom: 2rem;
}

.track-info h4 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
}

.track-info p {
    opacity: 0.8;
}

.track-progress {
    margin-top: 1rem;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(255,255,255,0.3);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #ff6b6b, #4ecdc4);
    border-radius: 3px;
    transition: width 0.3s ease;
    width: 0%;
}

.time-info {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    opacity: 0.8;
}

.control-btn {
    background: rgba(255,255,255,0.2);
    border: none;
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.2rem;
}

.control-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: scale(1.1);
}

.playlist-section {
    flex: 1;
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    height: fit-content;
}

.playlist-section h3 {
    color: #333;
    margin-bottom: 1rem;
    border-bottom: 2px solid #ff6b6b;
    padding-bottom: 0.5rem;
}

.playlist {
    margin-bottom: 2rem;
}

.playlist-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.playlist-item:hover {
    background: #f8f9fa;
    border-left-color: #ff6b6b;
}

.playlist-item.active {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    border-left-color: #ff6b6b;
}

.track-number {
    background: #ff6b6b;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
}

.track-details h5 {
    margin-bottom: 0.3rem;
    color: #333;
}

.track-details p {
    color: #666;
    font-size: 0.8rem;
}

.track-duration {
    margin-left: auto;
    color: #999;
    font-size: 0.9rem;
}

/* Search Results Styles */
.search-results {
    max-height: 400px;
    overflow-y: auto;
    margin-top: 1rem;
}

.search-result-item {
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-result-item:hover {
    background: #f8f9fa;
    border-color: #ff6b6b;
}

.result-type {
    background: #ff6b6b;
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    display: inline-block;
    margin-bottom: 0.5rem;
}

.search-result-item h4 {
    color: #333;
    margin-bottom: 0.5rem;
}

.search-result-item p {
    color: #666;
    margin-bottom: 0.5rem;
}

.result-meta {
    color: #999;
    font-size: 0.8rem;
}

.no-results {
    text-align: center;
    padding: 2rem;
    color: #666;
    font-style: italic;
}

/* Home Page Cards */
.news-card,
.video-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    cursor: pointer;
}

.news-card:hover,
.video-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.news-card img,
.video-thumbnail img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.video-thumbnail {
    position: relative;
}

.play-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 107, 107, 0.9);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.video-card:hover .play-overlay {
    background: rgba(255, 107, 107, 1);
    transform: translate(-50%, -50%) scale(1.1);
}

.news-card-content,
.video-card-content {
    padding: 1.5rem;
}

.news-card-content h3,
.video-card-content h3 {
    color: #333;
    margin-bottom: 0.8rem;
    font-size: 1.1rem;
    line-height: 1.3;
}

.news-card-content p,
.video-card-content p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 0.8rem;
}

.news-meta {
    color: #999;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.news-meta::before {
    content: '📅';
    font-size: 0.7rem;
}

/* Schedule Section Styles */
.schedule-section h3 {
    color: #333;
    margin-bottom: 1rem;
    border-bottom: 2px solid #4ecdc4;
    padding-bottom: 0.5rem;
}

.schedule-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.schedule-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.8rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.schedule-item:hover {
    background: #f8f9fa;
}

.schedule-item.active {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
    color: white;
}

.schedule-item .time {
    font-weight: bold;
    font-size: 0.9rem;
}

.schedule-item .program {
    font-size: 0.9rem;
}

/* Enhanced Home Page Styles */
.hero-content {
    display: flex;
    align-items: center;
    gap: 3rem;
    text-align: left;
}

.hero-text {
    flex: 1;
}

.hero-text h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    font-weight: 700;
    background: linear-gradient(135deg, #fff 0%, #f0f0f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-text p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.hero-stats {
    display: flex;
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    color: #ffd700;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.hero-images {
    flex: 1;
}

.image-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.hero-img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    transition: transform 0.3s ease;
}

.hero-img:hover {
    transform: scale(1.05);
}

.home-section {
    padding: 3rem 0;
    margin: 2rem 0;
}

.home-section.yellow-pages {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    border-radius: 20px;
    margin: 2rem;
    padding: 3rem 2rem;
}

.home-section.app-section {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border-radius: 20px;
    margin: 2rem;
    padding: 3rem 2rem;
}

.app-download {
    display: flex;
    align-items: center;
    gap: 3rem;
}

.app-info {
    flex: 1;
}

.app-info h2 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 1rem;
}

.app-info p {
    color: #666;
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.app-buttons {
    display: flex;
    gap: 1rem;
}

.app-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.app-btn.android {
    background: #4CAF50;
    color: white;
}

.app-btn.ios {
    background: #007AFF;
    color: white;
}

.app-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}
