// Hello Madurai Website JavaScript
class HelloMaduraiApp {
    constructor() {
        this.currentSection = 'home';
        this.currentCategory = null;
        this.currentPhotoIndex = 0;
        this.currentPhotos = [];
        this.isPlaying = false;
        
        this.init();
    }

    init() {
        this.setupNavigation();
        this.setupCategoryHandlers();
        this.setupModals();
        this.setupFMPlayer();
        this.loadInitialData();
        this.setupMobileMenu();
        this.setupSearch();
    }

    setupNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.getAttribute('data-section');
                this.showSection(section);
                this.updateActiveNav(link);
            });
        });
    }

    showSection(sectionId) {
        // Hide all sections
        document.querySelectorAll('.section').forEach(section => {
            section.classList.remove('active');
        });
        
        // Show target section
        const targetSection = document.getElementById(sectionId);
        if (targetSection) {
            targetSection.classList.add('active');
            this.currentSection = sectionId;
        }
    }

    updateActiveNav(activeLink) {
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        activeLink.classList.add('active');
    }

    setupCategoryHandlers() {
        // News sidebar items
        document.querySelectorAll('#news .sidebar-item').forEach(item => {
            item.addEventListener('click', () => {
                this.selectSidebarItem(item, 'news');
                this.loadNewsContent(item.getAttribute('data-category'));
            });
        });

        // Video sidebar items
        document.querySelectorAll('#video .sidebar-item').forEach(item => {
            item.addEventListener('click', () => {
                this.selectSidebarItem(item, 'video');
                this.loadVideoContent(item.getAttribute('data-category'));
            });
        });

        // Photo categories (keeping old style for photos)
        document.querySelectorAll('#photos .category-card').forEach(card => {
            card.addEventListener('click', () => {
                this.selectCategory(card, 'photos');
                this.loadPhotoContent(card.getAttribute('data-category'));
            });
        });

        // Helpline categories (keeping old style for helpline)
        document.querySelectorAll('#helpline .category-card').forEach(card => {
            card.addEventListener('click', () => {
                this.selectCategory(card, 'helpline');
                this.loadHelplineContent(card.getAttribute('data-category'));
            });
        });
    }

    selectSidebarItem(item, section) {
        // Remove active class from all sidebar items in the section
        document.querySelectorAll(`#${section} .sidebar-item`).forEach(i => {
            i.classList.remove('active');
        });

        // Add active class to selected item
        item.classList.add('active');
    }

    selectCategory(card, section) {
        // Remove active class from all cards in the section
        document.querySelectorAll(`#${section} .category-card`).forEach(c => {
            c.classList.remove('active');
        });
        
        // Add active class to selected card
        card.classList.add('active');
    }

    async loadNewsContent(category) {
        const newsContent = document.getElementById('news-content');
        newsContent.innerHTML = '<div class="loading"><div class="spinner"></div></div>';

        try {
            const articles = await this.getNewsData(category);
            newsContent.innerHTML = this.renderNewsArticles(articles);
            this.setupInteractionButtons();
        } catch (error) {
            console.error('Error loading news:', error);
            newsContent.innerHTML = '<div class="error-message">செய்திகளை ஏற்றுவதில் பிழை ஏற்பட்டது</div>';
        }
    }

    loadVideoContent(category) {
        const videoContent = document.getElementById('video-content');
        videoContent.innerHTML = '<div class="loading"><div class="spinner"></div></div>';
        
        setTimeout(() => {
            const videos = this.getVideoData(category);
            videoContent.innerHTML = this.renderVideos(videos);
            this.setupInteractionButtons();
        }, 500);
    }

    loadPhotoContent(category) {
        const photoGallery = document.getElementById('photo-gallery');
        photoGallery.innerHTML = '<div class="loading"><div class="spinner"></div></div>';
        
        setTimeout(() => {
            const photos = this.getPhotoData(category);
            photoGallery.innerHTML = this.renderPhotoGallery(photos);
            this.setupPhotoGallery();
            this.setupInteractionButtons();
        }, 500);
    }

    loadHelplineContent(category) {
        const helplineContent = document.getElementById('helpline-content');
        helplineContent.innerHTML = '<div class="loading"><div class="spinner"></div></div>';
        
        setTimeout(() => {
            const helplines = this.getHelplineData(category);
            helplineContent.innerHTML = this.renderHelplines(helplines);
        }, 500);
    }

    // Data methods
    async getNewsData(category) {
        // First try to get from database
        if (window.helloDB) {
            try {
                const dbNews = await window.helloDB.getNews(category);
                if (dbNews && dbNews.length > 0) {
                    return dbNews;
                }
            } catch (error) {
                console.error('Error fetching news from database:', error);
            }
        }

        // Check for admin data
        const adminData = this.getAdminData();
        if (adminData && adminData.news) {
            const adminNews = adminData.news.filter(news => news.category === category);
            if (adminNews.length > 0) {
                return adminNews;
            }
        }

        // Fallback to default data
        const newsData = {
            collector: [
                { title: 'கலெக்டர் அலுவலக புதிய அறிவிப்பு', content: 'மதுரை மாவட்ட கலெக்டர் அலுவலகத்தில் இருந்து புதிய அறிவிப்பு வெளியிடப்பட்டுள்ளது.', date: '2024-01-15', image: 'https://via.placeholder.com/300x200' },
                { title: 'வருவாய் துறை சேவைகள்', content: 'வருவாய் துறை சார்ந்த அனைத்து சேவைகளும் ஆன்லைன் மூலம் கிடைக்கும்.', date: '2024-01-14', image: 'https://via.placeholder.com/300x200' }
            ],
            corporation: [
                { title: 'மாநகராட்சி புதிய திட்டங்கள்', content: 'மதுரை மாநகராட்சியின் புதிய வளர்ச்சி திட்டங்கள் அறிவிக்கப்பட்டுள்ளன.', date: '2024-01-15', image: 'https://via.placeholder.com/300x200' },
                { title: 'சாலை மேம்பாட்டு பணிகள்', content: 'நகரின் முக்கிய சாலைகளில் மேம்பாட்டு பணிகள் தொடங்கப்பட்டுள்ளன.', date: '2024-01-13', image: 'https://via.placeholder.com/300x200' }
            ],
            police: [
                { title: 'போலீஸ் பாதுகாப்பு நடவடிக்கைகள்', content: 'மதுரை நகரில் பாதுகாப்பு நடவடிக்கைகள் தீவிரப்படுத்தப்பட்டுள்ளன.', date: '2024-01-15', image: 'https://via.placeholder.com/300x200' },
                { title: 'போக்குவரத்து விதிமுறைகள்', content: 'புதிய போக்குவரத்து விதிமுறைகள் அமல்படுத்தப்பட்டுள்ளன.', date: '2024-01-12', image: 'https://via.placeholder.com/300x200' }
            ],
            agriculture: [
                { title: 'விவசாய மானியங்கள்', content: 'விவசாயிகளுக்கான புதிய மானிய திட்டங்கள் அறிவிக்கப்பட்டுள்ளன.', date: '2024-01-15', image: 'https://via.placeholder.com/300x200' },
                { title: 'பயிர் காப்பீட்டு திட்டம்', content: 'விவசாயிகளுக்கான பயிர் காப்பீட்டு திட்டம் தொடங்கப்பட்டுள்ளது.', date: '2024-01-11', image: 'https://via.placeholder.com/300x200' }
            ],
            cinema: [
                { title: 'தமிழ் சினிமா செய்திகள்', content: 'தமிழ் சினிமா உலகின் சமீபத்திய செய்திகள் மற்றும் அப்டேட்கள்.', date: '2024-01-15', image: 'https://via.placeholder.com/300x200' },
                { title: 'புதிய திரைப்பட வெளியீடுகள்', content: 'இந்த வாரம் வெளியாகும் புதிய திரைப்படங்களின் விவரங்கள்.', date: '2024-01-14', image: 'https://via.placeholder.com/300x200' }
            ],
            articles: [
                { title: 'கல்வி கட்டுரைகள்', content: 'கல்வி மற்றும் வளர்ச்சி சார்ந்த கட்டுரைகள்.', date: '2024-01-15', image: 'https://via.placeholder.com/300x200' },
                { title: 'சமூக விழிப்புணர்வு', content: 'சமூக விழிப்புணர்வு மற்றும் மாற்றம் சார்ந்த கட்டுரைகள்.', date: '2024-01-13', image: 'https://via.placeholder.com/300x200' }
            ],
            jobs: [
                { title: 'அரசு வேலை வாய்ப்புகள்', content: 'தமிழ்நாடு அரசின் புதிய வேலை வாய்ப்பு அறிவிப்புகள்.', date: '2024-01-15', image: 'https://via.placeholder.com/300x200' },
                { title: 'தனியார் நிறுவன வேலைகள்', content: 'மதுரை மற்றும் சுற்றுப்பகுதிகளில் தனியார் வேலை வாய்ப்புகள்.', date: '2024-01-12', image: 'https://via.placeholder.com/300x200' }
            ]
        };
        
        return newsData[category] || [];
    }

    getVideoData(category) {
        // First check for admin data
        const adminData = this.getAdminData();
        if (adminData && adminData.videos) {
            const adminVideos = adminData.videos.filter(video => video.category === category);
            if (adminVideos.length > 0) {
                return adminVideos;
            }
        }

        // Fallback to default data
        const videoData = {
            business: [
                { title: 'தொழில் தொடங்கும் முறை', youtubeId: 'dQw4w9WgXcQ', description: 'புதிய தொழில் தொடங்குவதற்கான வழிமுறைகள்' },
                { title: 'வணிக வளர்ச்சி உத்திகள்', youtubeId: 'dQw4w9WgXcQ', description: 'வணிகத்தை வளர்ப்பதற்கான சிறந்த உத்திகள்' }
            ],
            agriculture: [
                { title: 'நவீன விவசாய முறைகள்', youtubeId: 'dQw4w9WgXcQ', description: 'நவீன தொழில்நுட்பம் பயன்படுத்திய விவசாயம்' },
                { title: 'இயற்கை விவசாயம்', youtubeId: 'dQw4w9WgXcQ', description: 'இயற்கை முறையில் விவசாயம் செய்வது எப்படி' }
            ],
            hotel: [
                { title: 'உணவு தயாரிப்பு முறைகள்', youtubeId: 'dQw4w9WgXcQ', description: 'பாரம்பரிய தமிழ் உணவு தயாரிப்பு' },
                { title: 'ஹோட்டல் மேலாண்மை', youtubeId: 'dQw4w9WgXcQ', description: 'ஹோட்டல் வணிகம் நடத்தும் முறைகள்' }
            ],
            medical: [
                { title: 'ஆரோக்கிய வாழ்க்கை முறை', youtubeId: 'dQw4w9WgXcQ', description: 'ஆரோக்கியமான வாழ்க்கை வாழ்வதற்கான வழிகள்' },
                { title: 'முதலுதவி முறைகள்', youtubeId: 'dQw4w9WgXcQ', description: 'அவசர காலத்தில் முதலுதவி செய்வது எப்படி' }
            ],
            vehicle: [
                { title: 'வாகன பராமரிப்பு', youtubeId: 'dQw4w9WgXcQ', description: 'வாகனங்களை பராமரிக்கும் முறைகள்' },
                { title: 'சாலை பாதுகாப்பு', youtubeId: 'dQw4w9WgXcQ', description: 'சாலையில் பாதுகாப்பாக பயணிக்கும் முறைகள்' }
            ],
            pets: [
                { title: 'செல்லப்பிராணி பராமரிப்பு', youtubeId: 'dQw4w9WgXcQ', description: 'செல்லப்பிராணிகளை பராமரிக்கும் முறைகள்' },
                { title: 'நாய் பயிற்சி', youtubeId: 'dQw4w9WgXcQ', description: 'நாய்களுக்கு பயிற்சி அளிக்கும் முறைகள்' }
            ]
        };
        
        return videoData[category] || [];
    }

    getPhotoData(category) {
        // First check for admin data
        const adminData = this.getAdminData();
        if (adminData && adminData.photos) {
            const adminPhotos = adminData.photos.filter(photo => photo.category === category);
            if (adminPhotos.length > 0) {
                return adminPhotos;
            }
        }

        // Fallback to default data
        const photoData = {
            chennai: [
                { src: 'https://via.placeholder.com/400x300', title: 'மெரினா கடற்கரை', description: 'சென்னையின் புகழ்பெற்ற மெரினா கடற்கரை' },
                { src: 'https://via.placeholder.com/400x300', title: 'கபாலீஸ்வரர் கோயில்', description: 'மயிலாப்பூரில் உள்ள பழமையான கோயில்' },
                { src: 'https://via.placeholder.com/400x300', title: 'சென்னை மத்திய ரயில் நிலையம்', description: 'சென்னையின் முக்கிய ரயில் நிலையம்' },
                { src: 'https://via.placeholder.com/400x300', title: 'வள்ளுவர் கோட்டம்', description: 'திருவள்ளுவர் நினைவு மண்டபம்' }
            ],
            tirupparankunram: [
                { src: 'https://via.placeholder.com/400x300', title: 'திருப்பரங்குன்றம் முருகன் கோயில்', description: 'ஆறு படை வீடுகளில் ஒன்று' },
                { src: 'https://via.placeholder.com/400x300', title: 'மலை அடிவாரம்', description: 'திருப்பரங்குன்றம் மலையின் அழகிய காட்சி' },
                { src: 'https://via.placeholder.com/400x300', title: 'கோயில் கோபுரம்', description: 'பழமையான கோயில் கோபுரம்' },
                { src: 'https://via.placeholder.com/400x300', title: 'பக்தர்கள்', description: 'கோயிலில் வழிபாடு செய்யும் பக்தர்கள்' }
            ],
            madurai: [
                { src: 'https://via.placeholder.com/400x300', title: 'மீனாட்சி அம்மன் கோயில்', description: 'மதுரையின் புகழ்பெற்ற மீனாட்சி கோயில்' },
                { src: 'https://via.placeholder.com/400x300', title: 'திருமலை நாயக்கர் மஹால்', description: 'நாயக்கர் கால அரண்மனை' },
                { src: 'https://via.placeholder.com/400x300', title: 'அழகர் கோயில்', description: 'அழகர் மலையில் உள்ள கோயில்' },
                { src: 'https://via.placeholder.com/400x300', title: 'வைகை நதி', description: 'மதுரை வழியாக ஓடும் வைகை நதி' }
            ]
        };
        
        return photoData[category] || [];
    }

    getHelplineData(category) {
        const helplineData = {
            police: [
                { name: 'வடபழனி காவல் நிலையம்', area: 'வடபழனி', phone: '044-24811234', emergency: true },
                { name: 'சைதாபேட்டை காவல் நிலையம்', area: 'சைதாபேட்டை', phone: '044-24825678', emergency: true },
                { name: 'அண்ணா நகர் காவல் நிலையம்', area: 'அண்ணா நகர்', phone: '044-26161234', emergency: true },
                { name: 'டி.நகர் காவல் நிலையம்', area: 'டி.நகர்', phone: '044-24341234', emergency: true }
            ],
            hospital: [
                { name: 'அரசு பொது மருத்துவமனை', area: 'மதுரை', phone: '0452-2530451', emergency: true },
                { name: 'அப்போலோ மருத்துவமனை', area: 'மதுரை', phone: '0452-2580001', emergency: false },
                { name: 'மீனாட்சி மிஷன் மருத்துவமனை', area: 'மதுரை', phone: '0452-2580001', emergency: false },
                { name: 'பாண்டியன் மருத்துவமனை', area: 'மதுரை', phone: '0452-2530451', emergency: false }
            ],
            bank: [
                { name: 'இந்திய ஸ்டேட் வங்கி', area: 'மெயின் பிராஞ்ச்', phone: '0452-2341234', emergency: false },
                { name: 'கனரா வங்கி', area: 'டவுன் ஹால்', phone: '0452-2345678', emergency: false },
                { name: 'ஐசிஐசிஐ வங்கி', area: 'அண்ணா நகர்', phone: '0452-2567890', emergency: false },
                { name: 'எச்டிஎஃப்சி வங்கி', area: 'கே.கே.நகர்', phone: '0452-2678901', emergency: false }
            ],
            'blood-bank': [
                { name: 'அரசு இரத்த வங்கி', area: 'அரசு மருத்துவமனை', phone: '0452-2530451', emergency: true },
                { name: 'ரெட் கிராஸ் இரத்த வங்கி', area: 'மதுரை', phone: '0452-2341234', emergency: true },
                { name: 'அப்போலோ இரத்த வங்கி', area: 'அப்போலோ மருத்துவமனை', phone: '0452-2580001', emergency: true }
            ],
            'old-age-home': [
                { name: 'ஹெல்ப் ஏஜ் இந்தியா', area: 'மதுரை', phone: '0452-2341234', emergency: false },
                { name: 'முதியவர்கள் நல இல்லம்', area: 'அண்ணா நகர்', phone: '0452-2345678', emergency: false },
                { name: 'சேவா சதன்', area: 'கே.கே.நகர்', phone: '0452-2567890', emergency: false }
            ],
            'child-care': [
                { name: 'குழந்தைகள் நல காப்பகம்', area: 'மதுரை', phone: '0452-2341234', emergency: false },
                { name: 'பால் பாலிகா இல்லம்', area: 'அண்ணா நகர்', phone: '0452-2345678', emergency: false },
                { name: 'சிசு விகாஸ் மையம்', area: 'கே.கே.நகர்', phone: '0452-2567890', emergency: false }
            ]
        };
        
        return helplineData[category] || [];
    }

    // Render methods
    renderNewsArticles(articles) {
        return articles.map(article => `
            <div class="content-card fade-in">
                <div class="content-header">
                    <h3 class="content-title">${article.title}</h3>
                    <div class="content-meta">${article.date}</div>
                </div>
                <div class="content-body">
                    <img src="${article.image}" alt="${article.title}" class="content-image">
                    <p>${article.content}</p>
                    ${this.renderInteractionBar('news', article.title)}
                </div>
            </div>
        `).join('');
    }

    renderVideos(videos) {
        return videos.map(video => `
            <div class="content-card fade-in">
                <div class="content-header">
                    <h3 class="content-title">${video.title}</h3>
                </div>
                <div class="content-body">
                    <div class="video-player">
                        <iframe class="video-iframe"
                                src="https://www.youtube.com/embed/${video.youtubeId}"
                                title="${video.title}"
                                frameborder="0"
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                allowfullscreen>
                        </iframe>
                    </div>
                    <p>${video.description}</p>
                    ${this.renderInteractionBar('video', video.title, false)}
                </div>
            </div>
        `).join('');
    }

    renderPhotoGallery(photos) {
        this.currentPhotos = photos;
        return `
            <div class="photo-grid">
                ${photos.map((photo, index) => `
                    <div class="photo-item" data-index="${index}">
                        <img src="${photo.src}" alt="${photo.title}">
                        <div class="photo-overlay">
                            <h4>${photo.title}</h4>
                            <p>${photo.description}</p>
                        </div>
                    </div>
                `).join('')}
            </div>
            ${this.renderInteractionBar('photos', 'Photo Gallery')}
        `;
    }

    renderHelplines(helplines) {
        return helplines.map(helpline => `
            <div class="helpline-item">
                <div class="helpline-info">
                    <h4>${helpline.name}</h4>
                    <p>${helpline.area}</p>
                </div>
                <a href="tel:${helpline.phone}" class="call-btn">
                    <i class="fas fa-phone"></i>
                    ${helpline.phone}
                </a>
            </div>
        `).join('');
    }

    renderInteractionBar(type, id, showDownload = true) {
        return `
            <div class="interaction-bar">
                <button class="interaction-btn comment-btn" data-type="${type}" data-id="${id}">
                    <i class="fas fa-comment"></i>
                    <span>கருத்து</span>
                </button>
                <button class="interaction-btn emoji-btn" data-type="${type}" data-id="${id}">
                    <i class="fas fa-smile"></i>
                    <span>Emoji</span>
                </button>
                <button class="interaction-btn share-btn" data-type="${type}" data-id="${id}">
                    <i class="fas fa-share"></i>
                    <span>பகிர்</span>
                </button>
                ${showDownload && type !== 'video' ? `
                    <button class="interaction-btn download-btn" data-type="${type}" data-id="${id}">
                        <i class="fas fa-download"></i>
                        <span>பதிவிறக்கம்</span>
                    </button>
                ` : ''}
            </div>
        `;
    }

    setupInteractionButtons() {
        // Comment buttons
        document.querySelectorAll('.comment-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.openCommentModal(btn.getAttribute('data-type'), btn.getAttribute('data-id'));
            });
        });

        // Emoji buttons
        document.querySelectorAll('.emoji-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.toggleEmojiPicker(e.target.closest('.emoji-btn'));
            });
        });

        // Share buttons
        document.querySelectorAll('.share-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.shareContent(btn.getAttribute('data-type'), btn.getAttribute('data-id'));
            });
        });

        // Download buttons
        document.querySelectorAll('.download-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.downloadContent(btn.getAttribute('data-type'), btn.getAttribute('data-id'));
            });
        });
    }

    setupPhotoGallery() {
        document.querySelectorAll('.photo-item').forEach(item => {
            item.addEventListener('click', () => {
                const index = parseInt(item.getAttribute('data-index'));
                this.openPhotoModal(index);
            });
        });
    }

    openPhotoModal(index) {
        this.currentPhotoIndex = index;
        const modal = document.getElementById('photoModal');
        const modalPhoto = document.getElementById('modalPhoto');

        modalPhoto.src = this.currentPhotos[index].src;
        modalPhoto.alt = this.currentPhotos[index].title;

        modal.style.display = 'block';

        // Setup navigation
        document.getElementById('prevPhoto').onclick = () => this.navigatePhoto(-1);
        document.getElementById('nextPhoto').onclick = () => this.navigatePhoto(1);
    }

    navigatePhoto(direction) {
        this.currentPhotoIndex += direction;

        if (this.currentPhotoIndex < 0) {
            this.currentPhotoIndex = this.currentPhotos.length - 1;
        } else if (this.currentPhotoIndex >= this.currentPhotos.length) {
            this.currentPhotoIndex = 0;
        }

        const modalPhoto = document.getElementById('modalPhoto');
        modalPhoto.src = this.currentPhotos[this.currentPhotoIndex].src;
        modalPhoto.alt = this.currentPhotos[this.currentPhotoIndex].title;
    }

    setupModals() {
        // Close modal functionality
        document.querySelectorAll('.close').forEach(closeBtn => {
            closeBtn.addEventListener('click', () => {
                closeBtn.closest('.modal').style.display = 'none';
            });
        });

        // Close modal when clicking outside
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.style.display = 'none';
            }
        });
    }

    openCommentModal(type, id) {
        const modal = document.getElementById('commentModal');
        modal.style.display = 'block';

        // Load existing comments
        this.loadComments(type, id);

        // Setup comment submission
        const submitBtn = modal.querySelector('.submit-comment');
        const textarea = modal.querySelector('textarea');

        submitBtn.onclick = () => {
            const comment = textarea.value.trim();
            if (comment) {
                this.addComment(type, id, comment);
                textarea.value = '';
            }
        };
    }

    loadComments(type, id) {
        const commentsList = document.getElementById('commentsList');
        // Simulate loading comments from storage
        const comments = this.getStoredComments(type, id);

        commentsList.innerHTML = comments.map(comment => `
            <div class="comment-item">
                <div class="comment-author">${comment.author}</div>
                <div class="comment-text">${comment.text}</div>
                <div class="comment-time">${comment.time}</div>
            </div>
        `).join('');
    }

    addComment(type, id, text) {
        const comment = {
            author: 'பயனர்',
            text: text,
            time: new Date().toLocaleString('ta-IN')
        };

        // Store comment (in real app, this would go to a database)
        this.storeComment(type, id, comment);

        // Reload comments
        this.loadComments(type, id);
    }

    getStoredComments(type, id) {
        const key = `comments_${type}_${id}`;
        const stored = localStorage.getItem(key);
        return stored ? JSON.parse(stored) : [];
    }

    storeComment(type, id, comment) {
        const key = `comments_${type}_${id}`;
        const comments = this.getStoredComments(type, id);
        comments.unshift(comment);
        localStorage.setItem(key, JSON.stringify(comments));
    }

    getAdminData() {
        // Get data exported from admin panel
        const adminData = localStorage.getItem('website_data');
        return adminData ? JSON.parse(adminData) : null;
    }

    toggleEmojiPicker(btn) {
        // Remove existing emoji picker
        const existingPicker = document.querySelector('.emoji-picker');
        if (existingPicker) {
            existingPicker.remove();
            return;
        }

        // Create emoji picker
        const emojiPicker = document.createElement('div');
        emojiPicker.className = 'emoji-picker';
        emojiPicker.style.display = 'block';

        const emojis = ['😀', '😂', '😍', '👍', '👎', '❤️', '🔥', '💯', '🎉', '👏', '🙏', '💪'];

        emojiPicker.innerHTML = `
            <div class="emoji-grid">
                ${emojis.map(emoji => `
                    <button class="emoji-btn-item" data-emoji="${emoji}">${emoji}</button>
                `).join('')}
            </div>
        `;

        // Position picker
        const rect = btn.getBoundingClientRect();
        emojiPicker.style.position = 'absolute';
        emojiPicker.style.top = (rect.bottom + window.scrollY) + 'px';
        emojiPicker.style.left = rect.left + 'px';

        document.body.appendChild(emojiPicker);

        // Handle emoji selection
        emojiPicker.querySelectorAll('.emoji-btn-item').forEach(emojiBtn => {
            emojiBtn.addEventListener('click', () => {
                this.addEmojiReaction(btn.getAttribute('data-type'), btn.getAttribute('data-id'), emojiBtn.getAttribute('data-emoji'));
                emojiPicker.remove();
            });
        });
    }

    addEmojiReaction(type, id, emoji) {
        // Store emoji reaction
        const key = `emoji_${type}_${id}`;
        const reactions = JSON.parse(localStorage.getItem(key) || '{}');
        reactions[emoji] = (reactions[emoji] || 0) + 1;
        localStorage.setItem(key, JSON.stringify(reactions));

        // Show feedback
        this.showToast(`${emoji} சேர்க்கப்பட்டது!`);
    }

    shareContent(type, id) {
        const url = window.location.href;
        const text = `Hello Madurai - ${type}: ${id}`;

        if (navigator.share) {
            navigator.share({
                title: 'Hello Madurai',
                text: text,
                url: url
            });
        } else {
            // Fallback for browsers that don't support Web Share API
            navigator.clipboard.writeText(`${text} - ${url}`).then(() => {
                this.showToast('லிங்க் நகலெடுக்கப்பட்டது!');
            });
        }
    }

    downloadContent(type, id) {
        // Simulate download functionality
        this.showToast('பதிவிறக்கம் தொடங்கப்பட்டது...');

        // In a real application, this would trigger actual file download
        setTimeout(() => {
            this.showToast('பதிவிறக்கம் முடிந்தது!');
        }, 2000);
    }

    setupFMPlayer() {
        // Initialize FM player with track-based system
        this.currentTrackIndex = 0;
        this.tracks = [
            { name: 'Hello Madurai Intro', type: 'Promo clip', duration: 30 },
            { name: 'Calm Tamil Instrumental 1', type: 'Background Music', duration: 120 },
            { name: 'Local Event Announcement Clip', type: 'Promo clip', duration: 45 },
            { name: 'Calm Tamil Instrumental 2', type: 'Background Music', duration: 180 }
        ];
        this.currentTime = 0;
        this.isPlaying = false;
        this.progressInterval = null;

        const playBtn = document.getElementById('playBtn');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const volumeSlider = document.getElementById('volumeSlider');

        playBtn.addEventListener('click', () => {
            this.toggleFMPlayer();
        });

        prevBtn.addEventListener('click', () => {
            this.previousTrack();
        });

        nextBtn.addEventListener('click', () => {
            this.nextTrack();
        });

        volumeSlider.addEventListener('input', (e) => {
            this.setVolume(e.target.value);
        });

        // Initialize playlist and current track
        this.loadPlaylist();
        this.updateCurrentTrack();
    }

    toggleFMPlayer() {
        const playBtn = document.getElementById('playBtn');
        const icon = playBtn.querySelector('i');

        if (this.isPlaying) {
            icon.className = 'fas fa-play';
            this.isPlaying = false;
            this.pauseProgress();
            this.showToast('ஒளிபரப்பு நிறுத்தப்பட்டது');
        } else {
            icon.className = 'fas fa-pause';
            this.isPlaying = true;
            this.startProgress();
            this.showToast('ஒளிபரப்பு தொடங்கப்பட்டது');
        }
    }

    previousTrack() {
        this.currentTrackIndex = this.currentTrackIndex > 0 ? this.currentTrackIndex - 1 : this.tracks.length - 1;
        this.currentTime = 0;
        this.updateCurrentTrack();
        this.updatePlaylist();
        if (this.isPlaying) {
            this.startProgress();
        }
    }

    nextTrack() {
        this.currentTrackIndex = (this.currentTrackIndex + 1) % this.tracks.length;
        this.currentTime = 0;
        this.updateCurrentTrack();
        this.updatePlaylist();
        if (this.isPlaying) {
            this.startProgress();
        }
    }

    updateCurrentTrack() {
        const currentTrack = this.tracks[this.currentTrackIndex];
        document.getElementById('currentTrackName').textContent = currentTrack.name;
        document.getElementById('currentTrackType').textContent = currentTrack.type;
        document.getElementById('totalTime').textContent = this.formatTime(currentTrack.duration);
        document.getElementById('currentTime').textContent = this.formatTime(this.currentTime);

        // Update progress bar
        const progressFill = document.getElementById('progressFill');
        const progress = (this.currentTime / currentTrack.duration) * 100;
        progressFill.style.width = progress + '%';
    }

    loadPlaylist() {
        const playlist = document.getElementById('playlist');
        playlist.innerHTML = this.tracks.map((track, index) => `
            <div class="playlist-item ${index === this.currentTrackIndex ? 'active' : ''}" data-index="${index}">
                <div class="track-number">${index + 1}</div>
                <div class="track-details">
                    <h5>${track.name}</h5>
                    <p>${track.type}</p>
                </div>
                <div class="track-duration">${this.formatTime(track.duration)}</div>
            </div>
        `).join('');

        // Add click handlers for playlist items
        playlist.querySelectorAll('.playlist-item').forEach(item => {
            item.addEventListener('click', () => {
                const index = parseInt(item.getAttribute('data-index'));
                this.currentTrackIndex = index;
                this.currentTime = 0;
                this.updateCurrentTrack();
                this.updatePlaylist();
                if (this.isPlaying) {
                    this.startProgress();
                }
            });
        });
    }

    updatePlaylist() {
        document.querySelectorAll('.playlist-item').forEach((item, index) => {
            if (index === this.currentTrackIndex) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
    }

    startProgress() {
        this.pauseProgress(); // Clear any existing interval
        this.progressInterval = setInterval(() => {
            const currentTrack = this.tracks[this.currentTrackIndex];
            this.currentTime++;

            if (this.currentTime >= currentTrack.duration) {
                this.nextTrack();
            } else {
                this.updateCurrentTrack();
            }
        }, 1000);
    }

    pauseProgress() {
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }
    }

    formatTime(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    setVolume(volume) {
        // In a real application, this would control actual audio volume
        console.log(`Volume set to: ${volume}%`);
    }

    updateNowPlaying() {
        const programs = [
            'மதுரை செய்திகள்',
            'தமிழ் பாடல்கள்',
            'விவசாய நேரம்',
            'கல்வி நேரம்',
            'பொழுதுபோக்கு நேரம்'
        ];

        let currentIndex = 0;
        const nowPlaying = document.getElementById('nowPlaying');

        setInterval(() => {
            if (this.isPlaying) {
                nowPlaying.textContent = programs[currentIndex];
                currentIndex = (currentIndex + 1) % programs.length;
            }
        }, 10000); // Update every 10 seconds
    }

    loadInitialData() {
        // Load magazine data
        this.loadMagazines();
    }

    loadMagazines() {
        const magazineGrid = document.getElementById('magazine-grid');

        // First check for admin data
        const adminData = this.getAdminData();
        let magazines = [];

        if (adminData && adminData.magazines && adminData.magazines.length > 0) {
            magazines = adminData.magazines;
        } else {
            // Fallback to default data
            magazines = [
                { title: 'ஜனவரி 2024', month: 'ஜனவரி', year: '2024', pdfUrl: '#' },
                { title: 'டிசம்பர் 2023', month: 'டிசம்பர்', year: '2023', pdfUrl: '#' },
                { title: 'நவம்பர் 2023', month: 'நவம்பர்', year: '2023', pdfUrl: '#' },
                { title: 'அக்டோபர் 2023', month: 'அக்டோபர்', year: '2023', pdfUrl: '#' }
            ];
        }

        magazineGrid.innerHTML = magazines.map(magazine => `
            <div class="magazine-item">
                <div class="magazine-cover">
                    <i class="fas fa-book-open"></i>
                    <h3>${magazine.title}</h3>
                </div>
                <div class="magazine-info">
                    <h3>${magazine.month} ${magazine.year}</h3>
                    <p>மாத இதழ் - PDF வடிவில்</p>
                    <button class="download-btn" onclick="app.downloadMagazine('${magazine.pdfUrl}', '${magazine.title}')">
                        <i class="fas fa-download"></i>
                        பதிவிறக்கம்
                    </button>
                </div>
                ${this.renderInteractionBar('magazine', magazine.title)}
            </div>
        `).join('');

        // Setup interaction buttons for magazines
        this.setupInteractionButtons();
    }

    downloadMagazine(pdfUrl, title) {
        // In a real application, this would download the actual PDF
        this.showToast(`${title} பதிவிறக்கம் தொடங்கப்பட்டது...`);

        // Simulate download
        setTimeout(() => {
            this.showToast('பதிவிறக்கம் முடிந்தது!');
        }, 2000);
    }

    setupMobileMenu() {
        const mobileToggle = document.querySelector('.mobile-menu-toggle');
        const nav = document.querySelector('.nav');

        mobileToggle.addEventListener('click', () => {
            nav.style.display = nav.style.display === 'block' ? 'none' : 'block';
        });

        // Close mobile menu when clicking on nav links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', () => {
                if (window.innerWidth <= 768) {
                    nav.style.display = 'none';
                }
            });
        });
    }

    showToast(message) {
        // Create toast notification
        const toast = document.createElement('div');
        toast.className = 'toast';
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #333;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            z-index: 3000;
            animation: slideIn 0.3s ease-out;
        `;

        document.body.appendChild(toast);

        // Remove toast after 3 seconds
        setTimeout(() => {
            toast.style.animation = 'slideOut 0.3s ease-out';
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 3000);
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new HelloMaduraiApp();
});

// Add CSS for toast animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }

    .emoji-picker {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 1rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        z-index: 1000;
    }

    .emoji-grid {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 0.5rem;
    }

    .emoji-btn-item {
        background: none;
        border: none;
        font-size: 1.5rem;
        padding: 0.5rem;
        border-radius: 4px;
        cursor: pointer;
        transition: background 0.3s ease;
    }

    .emoji-btn-item:hover {
        background: #f8f9fa;
    }

    .photo-modal-content {
        text-align: center;
        position: relative;
    }

    .photo-modal-content img {
        max-width: 100%;
        max-height: 70vh;
        border-radius: 8px;
    }

    .photo-navigation {
        position: absolute;
        top: 50%;
        width: 100%;
        display: flex;
        justify-content: space-between;
        transform: translateY(-50%);
        pointer-events: none;
    }

    .nav-btn {
        background: rgba(0,0,0,0.5);
        color: white;
        border: none;
        padding: 1rem;
        border-radius: 50%;
        cursor: pointer;
        font-size: 1.2rem;
        pointer-events: all;
        transition: background 0.3s ease;
    }

    .nav-btn:hover {
        background: rgba(0,0,0,0.7);
    }

    @media (max-width: 768px) {
        .nav {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: none;
            padding: 1rem;
        }

        .nav-list {
            flex-direction: column;
            gap: 0.5rem;
        }

        .nav-link {
            flex-direction: row;
            justify-content: flex-start;
            gap: 1rem;
            padding: 1rem;
        }
    }
`;
document.head.appendChild(style);

    setupSearch() {
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.querySelector('.search-btn');

        const performSearch = async () => {
            const query = searchInput.value.trim();
            if (query.length < 2) {
                this.showToast('குறைந்தது 2 எழுத்துகள் தேவை');
                return;
            }

            this.showToast('தேடுகிறது...');

            try {
                let results = [];
                if (window.helloDB) {
                    results = await window.helloDB.search(query);
                } else {
                    // Fallback search in localStorage
                    results = this.searchLocalStorage(query);
                }

                this.displaySearchResults(results, query);
            } catch (error) {
                console.error('Search error:', error);
                this.showToast('தேடுவதில் பிழை ஏற்பட்டது');
            }
        };

        searchBtn.addEventListener('click', performSearch);
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    }

    searchLocalStorage(query) {
        const results = [];
        const lowerQuery = query.toLowerCase();

        // Search in news
        const news = JSON.parse(localStorage.getItem('hellomadurai_news') || '[]');
        const newsResults = news.filter(item =>
            item.title.toLowerCase().includes(lowerQuery) ||
            item.content.toLowerCase().includes(lowerQuery)
        );
        results.push(...newsResults.map(item => ({...item, type: 'news'})));

        // Search in videos
        const videos = JSON.parse(localStorage.getItem('hellomadurai_videos') || '[]');
        const videoResults = videos.filter(item =>
            item.title.toLowerCase().includes(lowerQuery) ||
            item.description.toLowerCase().includes(lowerQuery)
        );
        results.push(...videoResults.map(item => ({...item, type: 'videos'})));

        return results.sort((a, b) => b.timestamp - a.timestamp);
    }

    displaySearchResults(results, query) {
        // Create search results modal
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.display = 'block';

        modal.innerHTML = `
            <div class="modal-content">
                <span class="close">&times;</span>
                <h3>தேடல் முடிவுகள்: "${query}"</h3>
                <div class="search-results">
                    ${results.length > 0 ?
                        results.map(result => `
                            <div class="search-result-item" data-type="${result.type}" data-id="${result.id}">
                                <div class="result-type">${result.type === 'news' ? 'செய்தி' : 'வீடியோ'}</div>
                                <h4>${result.title}</h4>
                                <p>${result.content || result.description || ''}</p>
                                <div class="result-meta">
                                    ${result.category ? `பிரிவு: ${this.getCategoryName(result.category)}` : ''}
                                </div>
                            </div>
                        `).join('') :
                        '<div class="no-results">தேடல் முடிவுகள் எதுவும் கிடைக்கவில்லை</div>'
                    }
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Setup close functionality
        modal.querySelector('.close').addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });

        // Setup result click handlers
        modal.querySelectorAll('.search-result-item').forEach(item => {
            item.addEventListener('click', () => {
                const type = item.getAttribute('data-type');
                const section = type === 'news' ? 'news' : 'video';
                this.showSection(section);
                document.body.removeChild(modal);
            });
        });
    }

    getCategoryName(category) {
        const categoryNames = {
            business: 'தொழில்',
            agriculture: 'விவசாயம்',
            hotel: 'ஹோட்டல்',
            medical: 'மருத்துவம்',
            vehicle: 'வாகனம்',
            pets: 'பெட்ஸ்',
            collector: 'கலெக்டர்',
            corporation: 'மாநகராட்சி',
            police: 'போலீஸ்',
            cinema: 'சினிமா',
            articles: 'கட்டுரைகள்',
            jobs: 'வேலை வாய்ப்பு',
            chennai: 'சென்னை',
            tirupparankunram: 'திருப்பரங்குன்றம்',
            madurai: 'மதுரை'
        };
        return categoryNames[category] || category;
    }
