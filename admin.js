// Hello Madurai Admin Panel JavaScript
class AdminPanel {
    constructor() {
        this.init();
    }

    init() {
        this.setupForms();
        this.loadExistingVideos();
    }

    setupForms() {
        // Video form
        document.getElementById('videoForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addVideo();
        });

        // News form
        document.getElementById('newsForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addNews();
        });

        // Magazine form
        document.getElementById('magazineForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addMagazine();
        });

        // Photo form
        document.getElementById('photoForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addPhoto();
        });
    }

    addVideo() {
        const category = document.getElementById('videoCategory').value;
        const title = document.getElementById('videoTitle').value;
        const youtubeUrl = document.getElementById('youtubeUrl').value;
        const description = document.getElementById('videoDescription').value;

        // Extract YouTube video ID
        const youtubeId = this.extractYouTubeId(youtubeUrl);
        if (!youtubeId) {
            this.showMessage('videoMessage', 'தவறான YouTube URL', 'error');
            return;
        }

        const video = {
            id: Date.now().toString(),
            category,
            title,
            youtubeId,
            description,
            dateAdded: new Date().toISOString()
        };

        // Store video
        this.storeVideo(video);
        
        // Clear form
        document.getElementById('videoForm').reset();
        
        // Show success message
        this.showMessage('videoMessage', 'வீடியோ வெற்றிகரமாக சேர்க்கப்பட்டது!', 'success');
        
        // Reload video list
        this.loadExistingVideos();
    }

    addNews() {
        const category = document.getElementById('newsCategory').value;
        const title = document.getElementById('newsTitle').value;
        const content = document.getElementById('newsContent').value;
        const image = document.getElementById('newsImage').value || 'https://via.placeholder.com/300x200';

        const news = {
            id: Date.now().toString(),
            category,
            title,
            content,
            image,
            date: new Date().toLocaleDateString('ta-IN'),
            dateAdded: new Date().toISOString()
        };

        // Store news
        this.storeNews(news);
        
        // Clear form
        document.getElementById('newsForm').reset();
        
        // Show success message
        this.showMessage('newsMessage', 'செய்தி வெற்றிகரமாக சேர்க்கப்பட்டது!', 'success');
    }

    addMagazine() {
        const month = document.getElementById('magazineMonth').value;
        const year = document.getElementById('magazineYear').value;
        const pdfFile = document.getElementById('magazinePdf').files[0];

        if (!pdfFile) {
            this.showMessage('magazineMessage', 'PDF கோப்பு தேர்ந்தெடுக்கவும்', 'error');
            return;
        }

        // In a real application, you would upload the file to a server
        const magazine = {
            id: Date.now().toString(),
            title: `${month} ${year}`,
            month,
            year,
            pdfUrl: '#', // This would be the actual uploaded file URL
            dateAdded: new Date().toISOString()
        };

        // Store magazine
        this.storeMagazine(magazine);
        
        // Clear form
        document.getElementById('magazineForm').reset();
        
        // Show success message
        this.showMessage('magazineMessage', 'மாத இதழ் வெற்றிகரமாக சேர்க்கப்பட்டது!', 'success');
    }

    addPhoto() {
        const category = document.getElementById('photoCategory').value;
        const title = document.getElementById('photoTitle').value;
        const description = document.getElementById('photoDescription').value;
        const photoFile = document.getElementById('photoFile').files[0];

        if (!photoFile) {
            this.showMessage('photoMessage', 'புகைப்படம் தேர்ந்தெடுக்கவும்', 'error');
            return;
        }

        // In a real application, you would upload the file to a server
        const photo = {
            id: Date.now().toString(),
            category,
            title,
            description,
            src: 'https://via.placeholder.com/400x300', // This would be the actual uploaded file URL
            dateAdded: new Date().toISOString()
        };

        // Store photo
        this.storePhoto(photo);
        
        // Clear form
        document.getElementById('photoForm').reset();
        
        // Show success message
        this.showMessage('photoMessage', 'புகைப்படம் வெற்றிகரமாக சேர்க்கப்பட்டது!', 'success');
    }

    extractYouTubeId(url) {
        const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
        const match = url.match(regExp);
        return (match && match[2].length === 11) ? match[2] : null;
    }

    storeVideo(video) {
        const videos = this.getStoredVideos();
        videos.push(video);
        localStorage.setItem('admin_videos', JSON.stringify(videos));
    }

    storeNews(news) {
        const newsItems = this.getStoredNews();
        newsItems.push(news);
        localStorage.setItem('admin_news', JSON.stringify(newsItems));
    }

    storeMagazine(magazine) {
        const magazines = this.getStoredMagazines();
        magazines.push(magazine);
        localStorage.setItem('admin_magazines', JSON.stringify(magazines));
    }

    storePhoto(photo) {
        const photos = this.getStoredPhotos();
        photos.push(photo);
        localStorage.setItem('admin_photos', JSON.stringify(photos));
    }

    getStoredVideos() {
        return JSON.parse(localStorage.getItem('admin_videos') || '[]');
    }

    getStoredNews() {
        return JSON.parse(localStorage.getItem('admin_news') || '[]');
    }

    getStoredMagazines() {
        return JSON.parse(localStorage.getItem('admin_magazines') || '[]');
    }

    getStoredPhotos() {
        return JSON.parse(localStorage.getItem('admin_photos') || '[]');
    }

    loadExistingVideos() {
        const videos = this.getStoredVideos();
        const videoList = document.getElementById('videoList');
        
        if (videos.length === 0) {
            videoList.innerHTML = '<p style="text-align: center; color: #666;">இதுவரை வீடியோக்கள் எதுவும் சேர்க்கப்படவில்லை</p>';
            return;
        }

        videoList.innerHTML = videos.map(video => `
            <div class="video-item">
                <div class="video-info">
                    <h4>${video.title}</h4>
                    <p>பிரிவு: ${this.getCategoryName(video.category)} | YouTube ID: ${video.youtubeId}</p>
                    <p>${video.description}</p>
                </div>
                <div class="video-actions">
                    <button class="btn-admin btn-small btn-edit" onclick="admin.editVideo('${video.id}')">
                        <i class="fas fa-edit"></i> திருத்து
                    </button>
                    <button class="btn-admin btn-small btn-delete" onclick="admin.deleteVideo('${video.id}')">
                        <i class="fas fa-trash"></i> நீக்கு
                    </button>
                </div>
            </div>
        `).join('');
    }

    getCategoryName(category) {
        const categoryNames = {
            business: 'தொழில்',
            agriculture: 'விவசாயம்',
            hotel: 'ஹோட்டல்',
            medical: 'மருத்துவம்',
            vehicle: 'வாகனம்',
            pets: 'பெட்ஸ்',
            collector: 'கலெக்டர்',
            corporation: 'மாநகராட்சி',
            police: 'போலீஸ்',
            cinema: 'சினிமா',
            articles: 'கட்டுரைகள்',
            jobs: 'வேலை வாய்ப்பு',
            chennai: 'சென்னை',
            tirupparankunram: 'திருப்பரங்குன்றம்',
            madurai: 'மதுரை'
        };
        return categoryNames[category] || category;
    }

    editVideo(videoId) {
        const videos = this.getStoredVideos();
        const video = videos.find(v => v.id === videoId);
        
        if (video) {
            // Populate form with video data
            document.getElementById('videoCategory').value = video.category;
            document.getElementById('videoTitle').value = video.title;
            document.getElementById('youtubeUrl').value = `https://www.youtube.com/watch?v=${video.youtubeId}`;
            document.getElementById('videoDescription').value = video.description;
            
            // Remove the video from storage (it will be re-added when form is submitted)
            this.deleteVideo(videoId);
            
            // Scroll to form
            document.getElementById('videoForm').scrollIntoView({ behavior: 'smooth' });
        }
    }

    deleteVideo(videoId) {
        const videos = this.getStoredVideos();
        const filteredVideos = videos.filter(v => v.id !== videoId);
        localStorage.setItem('admin_videos', JSON.stringify(filteredVideos));
        
        this.loadExistingVideos();
        this.showMessage('videoMessage', 'வீடியோ நீக்கப்பட்டது', 'success');
    }

    showMessage(elementId, message, type) {
        const element = document.getElementById(elementId);
        const className = type === 'success' ? 'success-message' : 'error-message';
        
        element.innerHTML = `<div class="${className}">${message}</div>`;
        
        // Clear message after 3 seconds
        setTimeout(() => {
            element.innerHTML = '';
        }, 3000);
    }

    // Export data for main website
    exportData() {
        const data = {
            videos: this.getStoredVideos(),
            news: this.getStoredNews(),
            magazines: this.getStoredMagazines(),
            photos: this.getStoredPhotos()
        };
        
        // In a real application, this would sync with the main website's database
        localStorage.setItem('website_data', JSON.stringify(data));
        
        this.showMessage('videoMessage', 'தரவு ஏற்றுமதி செய்யப்பட்டது!', 'success');
    }
}

// Initialize admin panel
document.addEventListener('DOMContentLoaded', () => {
    window.admin = new AdminPanel();
});

// Add export button functionality
document.addEventListener('DOMContentLoaded', () => {
    const exportBtn = document.createElement('button');
    exportBtn.className = 'btn-admin';
    exportBtn.innerHTML = '<i class="fas fa-download"></i> தரவு ஏற்றுமதி';
    exportBtn.style.position = 'fixed';
    exportBtn.style.bottom = '20px';
    exportBtn.style.right = '20px';
    exportBtn.style.zIndex = '1000';
    exportBtn.onclick = () => window.admin.exportData();
    
    document.body.appendChild(exportBtn);
});
