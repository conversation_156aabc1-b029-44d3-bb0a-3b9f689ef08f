// Firebase Database Integration for Hello Madurai
// This uses Firebase Realtime Database (free tier)

class HelloMaduraiDatabase {
    constructor() {
        // Firebase configuration (replace with your own config)
        this.firebaseConfig = {
            apiKey: "your-api-key-here",
            authDomain: "hello-madurai.firebaseapp.com",
            databaseURL: "https://hello-madurai-default-rtdb.firebaseio.com/",
            projectId: "hello-madurai",
            storageBucket: "hello-madurai.appspot.com",
            messagingSenderId: "123456789",
            appId: "your-app-id-here"
        };
        
        this.isFirebaseAvailable = false;
        this.initializeFirebase();
    }

    async initializeFirebase() {
        try {
            // Check if Firebase is available
            if (typeof firebase !== 'undefined') {
                firebase.initializeApp(this.firebaseConfig);
                this.database = firebase.database();
                this.isFirebaseAvailable = true;
                console.log('Firebase initialized successfully');
            } else {
                console.log('Firebase not available, using localStorage fallback');
                this.isFirebaseAvailable = false;
            }
        } catch (error) {
            console.error('Firebase initialization failed:', error);
            this.isFirebaseAvailable = false;
        }
    }

    // News Methods
    async saveNews(newsData) {
        if (this.isFirebaseAvailable) {
            try {
                const newsRef = this.database.ref('news');
                const newNewsRef = newsRef.push();
                await newNewsRef.set({
                    ...newsData,
                    id: newNewsRef.key,
                    timestamp: firebase.database.ServerValue.TIMESTAMP
                });
                return newNewsRef.key;
            } catch (error) {
                console.error('Error saving news to Firebase:', error);
                return this.saveToLocalStorage('news', newsData);
            }
        } else {
            return this.saveToLocalStorage('news', newsData);
        }
    }

    async getNews(category = null) {
        if (this.isFirebaseAvailable) {
            try {
                const newsRef = this.database.ref('news');
                const snapshot = await newsRef.once('value');
                const newsData = snapshot.val();
                
                if (!newsData) return [];
                
                const newsArray = Object.values(newsData);
                
                if (category && category !== 'all') {
                    return newsArray.filter(news => news.category === category);
                }
                
                return newsArray.sort((a, b) => b.timestamp - a.timestamp);
            } catch (error) {
                console.error('Error fetching news from Firebase:', error);
                return this.getFromLocalStorage('news', category);
            }
        } else {
            return this.getFromLocalStorage('news', category);
        }
    }

    // Video Methods
    async saveVideo(videoData) {
        if (this.isFirebaseAvailable) {
            try {
                const videosRef = this.database.ref('videos');
                const newVideoRef = videosRef.push();
                await newVideoRef.set({
                    ...videoData,
                    id: newVideoRef.key,
                    timestamp: firebase.database.ServerValue.TIMESTAMP
                });
                return newVideoRef.key;
            } catch (error) {
                console.error('Error saving video to Firebase:', error);
                return this.saveToLocalStorage('videos', videoData);
            }
        } else {
            return this.saveToLocalStorage('videos', videoData);
        }
    }

    async getVideos(category = null) {
        if (this.isFirebaseAvailable) {
            try {
                const videosRef = this.database.ref('videos');
                const snapshot = await videosRef.once('value');
                const videosData = snapshot.val();
                
                if (!videosData) return [];
                
                const videosArray = Object.values(videosData);
                
                if (category && category !== 'all') {
                    return videosArray.filter(video => video.category === category);
                }
                
                return videosArray.sort((a, b) => b.timestamp - a.timestamp);
            } catch (error) {
                console.error('Error fetching videos from Firebase:', error);
                return this.getFromLocalStorage('videos', category);
            }
        } else {
            return this.getFromLocalStorage('videos', category);
        }
    }

    // Comments Methods
    async saveComment(type, itemId, commentData) {
        if (this.isFirebaseAvailable) {
            try {
                const commentsRef = this.database.ref(`comments/${type}/${itemId}`);
                const newCommentRef = commentsRef.push();
                await newCommentRef.set({
                    ...commentData,
                    id: newCommentRef.key,
                    timestamp: firebase.database.ServerValue.TIMESTAMP
                });
                return newCommentRef.key;
            } catch (error) {
                console.error('Error saving comment to Firebase:', error);
                return this.saveCommentToLocalStorage(type, itemId, commentData);
            }
        } else {
            return this.saveCommentToLocalStorage(type, itemId, commentData);
        }
    }

    async getComments(type, itemId) {
        if (this.isFirebaseAvailable) {
            try {
                const commentsRef = this.database.ref(`comments/${type}/${itemId}`);
                const snapshot = await commentsRef.once('value');
                const commentsData = snapshot.val();
                
                if (!commentsData) return [];
                
                return Object.values(commentsData).sort((a, b) => b.timestamp - a.timestamp);
            } catch (error) {
                console.error('Error fetching comments from Firebase:', error);
                return this.getCommentsFromLocalStorage(type, itemId);
            }
        } else {
            return this.getCommentsFromLocalStorage(type, itemId);
        }
    }

    // Magazine Methods
    async saveMagazine(magazineData) {
        if (this.isFirebaseAvailable) {
            try {
                const magazinesRef = this.database.ref('magazines');
                const newMagazineRef = magazinesRef.push();
                await newMagazineRef.set({
                    ...magazineData,
                    id: newMagazineRef.key,
                    timestamp: firebase.database.ServerValue.TIMESTAMP
                });
                return newMagazineRef.key;
            } catch (error) {
                console.error('Error saving magazine to Firebase:', error);
                return this.saveToLocalStorage('magazines', magazineData);
            }
        } else {
            return this.saveToLocalStorage('magazines', magazineData);
        }
    }

    async getMagazines() {
        if (this.isFirebaseAvailable) {
            try {
                const magazinesRef = this.database.ref('magazines');
                const snapshot = await magazinesRef.once('value');
                const magazinesData = snapshot.val();
                
                if (!magazinesData) return [];
                
                return Object.values(magazinesData).sort((a, b) => b.timestamp - a.timestamp);
            } catch (error) {
                console.error('Error fetching magazines from Firebase:', error);
                return this.getFromLocalStorage('magazines');
            }
        } else {
            return this.getFromLocalStorage('magazines');
        }
    }

    // Photo Methods
    async savePhoto(photoData) {
        if (this.isFirebaseAvailable) {
            try {
                const photosRef = this.database.ref('photos');
                const newPhotoRef = photosRef.push();
                await newPhotoRef.set({
                    ...photoData,
                    id: newPhotoRef.key,
                    timestamp: firebase.database.ServerValue.TIMESTAMP
                });
                return newPhotoRef.key;
            } catch (error) {
                console.error('Error saving photo to Firebase:', error);
                return this.saveToLocalStorage('photos', photoData);
            }
        } else {
            return this.saveToLocalStorage('photos', photoData);
        }
    }

    async getPhotos(category = null) {
        if (this.isFirebaseAvailable) {
            try {
                const photosRef = this.database.ref('photos');
                const snapshot = await photosRef.once('value');
                const photosData = snapshot.val();
                
                if (!photosData) return [];
                
                const photosArray = Object.values(photosData);
                
                if (category) {
                    return photosArray.filter(photo => photo.category === category);
                }
                
                return photosArray.sort((a, b) => b.timestamp - a.timestamp);
            } catch (error) {
                console.error('Error fetching photos from Firebase:', error);
                return this.getFromLocalStorage('photos', category);
            }
        } else {
            return this.getFromLocalStorage('photos', category);
        }
    }

    // LocalStorage Fallback Methods
    saveToLocalStorage(type, data) {
        const key = `hellomadurai_${type}`;
        const existing = JSON.parse(localStorage.getItem(key) || '[]');
        const newItem = {
            ...data,
            id: Date.now().toString(),
            timestamp: Date.now()
        };
        existing.push(newItem);
        localStorage.setItem(key, JSON.stringify(existing));
        return newItem.id;
    }

    getFromLocalStorage(type, category = null) {
        const key = `hellomadurai_${type}`;
        const data = JSON.parse(localStorage.getItem(key) || '[]');
        
        if (category && category !== 'all') {
            return data.filter(item => item.category === category);
        }
        
        return data.sort((a, b) => b.timestamp - a.timestamp);
    }

    saveCommentToLocalStorage(type, itemId, commentData) {
        const key = `hellomadurai_comments_${type}_${itemId}`;
        const existing = JSON.parse(localStorage.getItem(key) || '[]');
        const newComment = {
            ...commentData,
            id: Date.now().toString(),
            timestamp: Date.now()
        };
        existing.push(newComment);
        localStorage.setItem(key, JSON.stringify(existing));
        return newComment.id;
    }

    getCommentsFromLocalStorage(type, itemId) {
        const key = `hellomadurai_comments_${type}_${itemId}`;
        const comments = JSON.parse(localStorage.getItem(key) || '[]');
        return comments.sort((a, b) => b.timestamp - a.timestamp);
    }

    // Search functionality
    async search(query, type = 'all') {
        const results = [];
        
        if (type === 'all' || type === 'news') {
            const news = await this.getNews();
            const newsResults = news.filter(item => 
                item.title.toLowerCase().includes(query.toLowerCase()) ||
                item.content.toLowerCase().includes(query.toLowerCase())
            );
            results.push(...newsResults.map(item => ({...item, type: 'news'})));
        }
        
        if (type === 'all' || type === 'videos') {
            const videos = await this.getVideos();
            const videoResults = videos.filter(item => 
                item.title.toLowerCase().includes(query.toLowerCase()) ||
                item.description.toLowerCase().includes(query.toLowerCase())
            );
            results.push(...videoResults.map(item => ({...item, type: 'videos'})));
        }
        
        return results.sort((a, b) => b.timestamp - a.timestamp);
    }
}

// Initialize database
window.helloDB = new HelloMaduraiDatabase();
