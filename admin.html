<!DOCTYPE html>
<html lang="ta">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hello Madurai - Admin Panel</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Tamil:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .admin-container {
            max-width: 1000px;
            margin: 100px auto 50px;
            padding: 0 20px;
        }
        
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .admin-section {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .admin-section h3 {
            color: #333;
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-family: inherit;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn-admin {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-admin:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .video-list {
            display: grid;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .video-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .video-info h4 {
            margin-bottom: 0.3rem;
            color: #333;
        }
        
        .video-info p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .video-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn-small {
            padding: 0.5rem 1rem;
            font-size: 0.8rem;
            border-radius: 5px;
        }
        
        .btn-edit {
            background: #ffc107;
            color: #333;
        }
        
        .btn-delete {
            background: #dc3545;
            color: white;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border-left: 4px solid #28a745;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border-left: 4px solid #dc3545;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1><i class="fas fa-cog"></i> Hello Madurai - Admin Panel</h1>
            <p>வீடியோ மற்றும் உள்ளடக்க மேலாண்மை</p>
        </div>

        <!-- Video Management Section -->
        <div class="admin-section">
            <h3><i class="fas fa-video"></i> வீடியோ மேலாண்மை</h3>
            
            <form id="videoForm">
                <div class="form-group">
                    <label for="videoCategory">பிரிவு</label>
                    <select id="videoCategory" required>
                        <option value="">பிரிவு தேர்ந்தெடுக்கவும்</option>
                        <option value="business">தொழில்</option>
                        <option value="agriculture">விவசாயம்</option>
                        <option value="hotel">ஹோட்டல்</option>
                        <option value="medical">மருத்துவம்</option>
                        <option value="vehicle">வாகனம்</option>
                        <option value="pets">பெட்ஸ்</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="videoTitle">வீடியோ தலைப்பு</label>
                    <input type="text" id="videoTitle" required placeholder="வீடியோ தலைப்பை உள்ளிடவும்">
                </div>
                
                <div class="form-group">
                    <label for="youtubeUrl">YouTube URL</label>
                    <input type="url" id="youtubeUrl" required placeholder="https://www.youtube.com/watch?v=...">
                </div>
                
                <div class="form-group">
                    <label for="videoDescription">விளக்கம்</label>
                    <textarea id="videoDescription" rows="3" placeholder="வீடியோ விளக்கம்"></textarea>
                </div>
                
                <button type="submit" class="btn-admin">
                    <i class="fas fa-plus"></i> வீடியோ சேர்க்க
                </button>
            </form>
            
            <div id="videoMessage"></div>
            
            <div class="video-list" id="videoList">
                <!-- Videos will be loaded here -->
            </div>
        </div>

        <!-- News Management Section -->
        <div class="admin-section">
            <h3><i class="fas fa-newspaper"></i> செய்தி மேலாண்மை</h3>
            
            <form id="newsForm">
                <div class="form-group">
                    <label for="newsCategory">பிரிவு</label>
                    <select id="newsCategory" required>
                        <option value="">பிரிவு தேர்ந்தெடுக்கவும்</option>
                        <option value="collector">கலெக்டர்</option>
                        <option value="corporation">மாநகராட்சி</option>
                        <option value="police">போலீஸ்</option>
                        <option value="agriculture">விவசாயம்</option>
                        <option value="cinema">சினிமா</option>
                        <option value="articles">கட்டுரைகள்</option>
                        <option value="jobs">வேலை வாய்ப்பு</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="newsTitle">செய்தி தலைப்பு</label>
                    <input type="text" id="newsTitle" required placeholder="செய்தி தலைப்பை உள்ளிடவும்">
                </div>
                
                <div class="form-group">
                    <label for="newsContent">செய்தி உள்ளடக்கம்</label>
                    <textarea id="newsContent" rows="5" required placeholder="செய்தி உள்ளடக்கம்"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="newsImage">படம் URL</label>
                    <input type="url" id="newsImage" placeholder="https://example.com/image.jpg">
                </div>
                
                <button type="submit" class="btn-admin">
                    <i class="fas fa-plus"></i> செய்தி சேர்க்க
                </button>
            </form>
            
            <div id="newsMessage"></div>
        </div>

        <!-- Magazine Management Section -->
        <div class="admin-section">
            <h3><i class="fas fa-book"></i> மாத இதழ் மேலாண்மை</h3>
            
            <form id="magazineForm">
                <div class="form-group">
                    <label for="magazineMonth">மாதம்</label>
                    <input type="text" id="magazineMonth" required placeholder="ஜனவரி">
                </div>
                
                <div class="form-group">
                    <label for="magazineYear">ஆண்டு</label>
                    <input type="number" id="magazineYear" required placeholder="2024">
                </div>
                
                <div class="form-group">
                    <label for="magazinePdf">PDF File</label>
                    <input type="file" id="magazinePdf" accept=".pdf" required>
                </div>
                
                <button type="submit" class="btn-admin">
                    <i class="fas fa-upload"></i> இதழ் பதிவேற்ற
                </button>
            </form>
            
            <div id="magazineMessage"></div>
        </div>

        <!-- Photo Management Section -->
        <div class="admin-section">
            <h3><i class="fas fa-camera"></i> புகைப்பட மேலாண்மை</h3>
            
            <form id="photoForm">
                <div class="form-group">
                    <label for="photoCategory">இடம்</label>
                    <select id="photoCategory" required>
                        <option value="">இடம் தேர்ந்தெடுக்கவும்</option>
                        <option value="chennai">சென்னை</option>
                        <option value="tirupparankunram">திருப்பரங்குன்றம்</option>
                        <option value="madurai">மதுரை</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="photoTitle">புகைப்பட தலைப்பு</label>
                    <input type="text" id="photoTitle" required placeholder="புகைப்பட தலைப்பு">
                </div>
                
                <div class="form-group">
                    <label for="photoDescription">விளக்கம்</label>
                    <input type="text" id="photoDescription" placeholder="புகைப்பட விளக்கம்">
                </div>
                
                <div class="form-group">
                    <label for="photoFile">புகைப்படம்</label>
                    <input type="file" id="photoFile" accept="image/*" required>
                </div>
                
                <button type="submit" class="btn-admin">
                    <i class="fas fa-upload"></i> புகைப்படம் பதிவேற்ற
                </button>
            </form>
            
            <div id="photoMessage"></div>
        </div>
    </div>

    <script src="admin.js"></script>
</body>
</html>
