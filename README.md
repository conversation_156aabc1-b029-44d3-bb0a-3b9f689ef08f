# Hello Madurai Website - ஹலோ மதுரை

A comprehensive Tamil website for Madurai with all the requested features including news, videos, FM radio, magazine, photos, and helpline services.

## Features / அம்சங்கள்

### 1. முகப்பு (Home)
- Welcome page with feature highlights
- Modern responsive design
- Tamil language support

### 2. செய்திகள் (News)
- **Categories:**
  - கலெக்டர் (Collector)
  - மாநகராட்சி (Corporation)
  - போலீஸ் (Police)
  - விவசாயம் (Agriculture)
  - சினிமா (Cinema)
  - கட்டுரைகள் (Articles)
  - வேலை வாய்ப்பு (Job Opportunities)
- Comment system with emoji reactions
- Share functionality
- Download option

### 3. வீடியோ (Video)
- **Categories:**
  - தொழில் (Business)
  - விவசாயம் (Agriculture)
  - ஹோட்டல் (Hotel)
  - மருத்துவம் (Medical)
  - வாகனம் (Vehicle)
  - பெட்ஸ் (Pets)
- YouTube integration with automatic link updates
- Comment system with emoji reactions
- Share functionality (NO download option as requested)

### 4. வானொலி (FM Radio)
- Live broadcast simulation
- Play/Pause controls
- Volume control
- Now playing information
- Modern player interface

### 5. மாத இதழ் (Magazine)
- Monthly magazine issues in PDF format
- Download functionality
- Admin panel for uploading new issues
- Comment and sharing features

### 6. புகைப்படங்கள் (Photos)
- **Location Categories:**
  - சென்னை (Chennai)
  - திருப்பரங்குன்றம் (Tirupparankunram)
  - மதுரை (Madurai)
- Photo gallery with modal view
- Navigation between photos
- Comment and sharing features

### 7. உதவி எண் (Help Line)
- **Categories:**
  - காவல் துறை (Police Department)
  - மருத்துவமனை (Hospitals)
  - வங்கி (Banks)
  - இரத்த வங்கி (Blood Banks)
  - முதியவர்கள் இல்லம் (Old Age Homes)
  - குழந்தைகள் நல காப்பகம் (Child Care Centers)
- Direct call functionality
- Area-wise contact numbers

## Technical Features

### Interactive Elements
- **Comment System:** Available on all content sections
- **Emoji Reactions:** 😀😂😍👍👎❤️🔥💯🎉👏🙏💪
- **Share Functionality:** Web Share API with fallback
- **Download Options:** Available for news, photos, and magazines (NOT for videos)

### Admin Panel
- **Video Management:** Add/Edit/Delete YouTube videos with automatic link updates
- **News Management:** Add/Edit news articles
- **Magazine Management:** Upload PDF magazines
- **Photo Management:** Upload and organize photos
- **Data Export:** Sync content with main website

### Responsive Design
- Mobile-first approach
- Tablet and desktop optimized
- Touch-friendly interface
- Modern CSS Grid and Flexbox

### Performance
- Lazy loading for images
- Optimized animations
- Local storage for offline functionality
- Fast loading times

## File Structure

```
hello_madurai/
├── index.html          # Main website
├── admin.html          # Admin panel
├── styles.css          # Main stylesheet
├── script.js           # Main JavaScript
├── admin.js            # Admin panel JavaScript
└── README.md           # This file
```

## How to Use

### For Users
1. Open `index.html` in a web browser
2. Navigate through different sections using the top menu
3. Click on category cards to view content
4. Use comment, emoji, and share features on content
5. Download magazines and photos as needed

### For Administrators
1. Open `admin.html` in a web browser
2. Add new videos by entering YouTube URLs
3. Add news articles with images
4. Upload magazine PDFs
5. Upload photos for different locations
6. Click "தரவு ஏற்றுமதி" (Export Data) to sync with main website

### YouTube Video Integration
1. In admin panel, paste any YouTube URL
2. System automatically extracts video ID
3. Videos appear immediately on main website
4. Support for all YouTube URL formats:
   - https://www.youtube.com/watch?v=VIDEO_ID
   - https://youtu.be/VIDEO_ID
   - https://www.youtube.com/embed/VIDEO_ID

## Browser Support
- Chrome (recommended)
- Firefox
- Safari
- Edge
- Mobile browsers

## Language Support
- Primary: Tamil (தமிழ்)
- Secondary: English for technical elements
- Tamil fonts: Noto Sans Tamil

## Future Enhancements
- Real FM radio streaming integration
- User authentication system
- Advanced content management
- Push notifications
- Offline mode
- Search functionality
- Multi-language support

## Installation
1. Download all files to a folder
2. Open `index.html` in a web browser
3. For admin access, open `admin.html`
4. No server setup required - works with file:// protocol

## Notes
- All data is stored in browser's localStorage
- For production use, integrate with a backend database
- YouTube videos require internet connection
- Admin panel is for demonstration - add authentication for production use

---

**Developed for Hello Madurai Community**
**Built with modern web technologies and Tamil language support**
